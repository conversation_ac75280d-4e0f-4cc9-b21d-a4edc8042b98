﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NursingManagement.Models
{
    /// <summary>
    /// 考核记录表
    /// </summary>
    [Table("ExaminationRecord")]
    public class ExaminationRecordInfo : MutiModifyInfo
    {
        /// <summary>
        /// 考核记录ID
        /// </summary>
        [Key]
        [Column(TypeName = "varchar(32)")]
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 试卷主记录ID，可为空
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string ExaminationPaperMainID { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartDateTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndDateTime { get; set; }

        /// <summary>
        /// 考核时长
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal Duration { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string Instructions { get; set; }

        /// <summary>
        /// 考核类型(1.试卷考核2.操作考核3.个人练习4.刷题练习)(配置在SettingDictionary)
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string Type { get; set; }

        /// <summary>
        /// 总分数
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal TotalScore { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ExaminationName { get; set; }

        /// <summary>
        /// 及格分数
        /// </summary>
        [Column(TypeName = "decimal(6,2)")]
        public decimal? PassingScore { get; set; }

        /// <summary>
        /// 最低答卷时长
        /// </summary>
        public short? MinAnswerTime { get; set; }

        /// <summary>
        /// 组织部门编码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string OrganizationalDepartmentCode { get; set; }

        /// <summary>
        /// 是否需要签到标记
        /// </summary>
        public bool SignInFlag { get; set; }

        /// <summary>
        /// 二维码刷新时间（单位：秒）
        /// </summary>
        public int QRCodeRefreshTime { get; set; }

        /// <summary>
        /// 发布状态
        /// </summary>
        public bool PublishFlag { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentID { get; set; }

        /// <summary>
        /// 考核级别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string ExaminationLevel { get; set; }

        /// <summary>
        /// 一页一题标记
        /// </summary>
        public bool? OnePageQuestionFlag { get; set; }
        /// <summary>
        /// 考核地点
        /// </summary>
        [Column(TypeName = "varchar(100)")]

        public string Location { get; set; }
        /// <summary>
        /// 分组分批关联ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]

        public string GroupID { get; set; }
    }
}
