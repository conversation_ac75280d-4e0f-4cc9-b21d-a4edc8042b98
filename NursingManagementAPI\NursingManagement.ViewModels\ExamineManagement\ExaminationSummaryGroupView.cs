namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 考核汇总视图 - 用于ElementPlus嵌套表格的第一层数据
    /// </summary>
    public class ExaminationSummaryView
    {
        /// <summary>
        /// 分组ID
        /// </summary>
        public string GroupID { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string GroupName { get; set; }

        /// <summary>
        /// 考核记录ID
        /// </summary>
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        public string ExamineName { get; set; }

        /// <summary>
        /// 考核级别名称
        /// </summary>
        public string ExaminationLevelName { get; set; }

        /// <summary>
        /// 监考人姓名
        /// </summary>
        public string ExaminerName { get; set; }

        /// <summary>
        /// 应参加人数
        /// </summary>
        public int ShouldParticipateCount { get; set; }

        /// <summary>
        /// 实际参加人数
        /// </summary>
        public int ParticipateCount { get; set; }

        /// <summary>
        /// 未参加人数
        /// </summary>
        public int NotParticipateCount { get; set; }

        /// <summary>
        /// 合格人数（实操类为达标人数）
        /// </summary>
        public int PassCount { get; set; }

        /// <summary>
        /// 参考率（百分比）
        /// 计算公式：有考核成绩（每人最多一次）/ 应参加人数
        /// </summary>
        public double ParticipationRate { get; set; }

        /// <summary>
        /// 合格率（百分比）
        /// 计算公式：合格人数（包含补考） / 参加人数
        /// </summary>
        public double PassRate { get; set; }

        /// <summary>
        /// 集中考核参加率（百分比）
        /// 计算公式：非补考人数 / 有考核成绩（每人最多一次）
        /// </summary>
        public double CentralExamParticipationRate { get; set; }

        /// <summary>
        /// 首次考核合格率（百分比）
        /// 计算公式：成绩合格并且只有一次考核记录 / 有考核成绩（每人最多一次）
        /// </summary>
        public double FirstExamPassRate { get; set; }

        /// <summary>
        /// 考核类型
        /// </summary>
        public string ExaminationType { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public int BatchNumber { get; set; }

        /// <summary>
        /// 子表格数据 - 用于ElementPlus嵌套表格展开显示
        /// </summary>
        public List<ExaminationBatchDetailView> Children { get; set; } = new List<ExaminationBatchDetailView>();
    }
}
