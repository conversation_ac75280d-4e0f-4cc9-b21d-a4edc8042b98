﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationMainRepository
    {
        /// <summary>
        /// 根据考核记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListByRecordID(string recordID);

        /// <summary>
        /// 根据考核记录ID集合获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListByRecordIDList(List<string> recordIDs);

        /// <summary>
        /// 根据考核记录ID集合获取补考相关字段数据
        /// </summary>
        /// <param name="recordIDs">考核记录ID集合</param>
        /// <returns>包含补考判断所需字段的考核主记录</returns>
        Task<List<ExaminationMainInfo>> GetRetakeFieldsByRecordIDList(List<string> recordIDs);

        /// <summary>
        /// 根据考核主表ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        Task<ExaminationMainInfo> GetDataByMainID(string mainID);

        /// <summary>
        /// 根据被考核人和考核状态获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="statusCodes"></param>
        /// <param name="examinationType">考核类型</param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListAsNoTrackByEmployeeIDAndType(string employeeID, List<string> statusCodes, string examinationType);

        /// <summary>
        /// 根据主考人获取数据
        /// </summary>
        /// <param name="examineEmployeeID"></param>
        /// <param name="statusCodes"></param>
        /// <param name="examineType"></param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListByExaminer(string examineEmployeeID, List<string> statusCodes, string examineType);
        /// <summary>
        /// 根据考核记录ID和人员ID获取人员考核记录
        /// </summary>
        /// <param name="recordID">考核主记录ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        Task<ExaminationMainInfo> GetEmployeeExaminationMainInfo(string recordID, string employeeID);

        /// <summary>
        /// 根据考核类别筛选数据
        /// </summary>
        /// <param name="employeeID">考核人工号</param>
        /// <param name="statusCodes">考核状态</param>
        /// <param name="examineType">考核类型</param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListByType(string employeeID, List<string> statusCodes, string examineType);

        /// <summary>
        /// 根据时间范围获取数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetListByDate(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 获取一组 <see cref="ExaminationMainInfo"/> 对象的列表，该列表不进行追踪， 过滤条件为指定的记录 ID 和状态码。
        /// </summary>
        /// <param name="recordIDs">要过滤结果的记录 ID 列表。</param>
        /// <param name="statusCodes">要过滤结果的状态码列表。</param>
        /// <returns>一个表示异步操作的任务，返回包含符合指定参数的 <see cref="ExaminationMainInfo"/> 对象列表。</returns>
        Task<List<ExaminationMainInfo>> GetListAsNoTrackByRecordIDAndStatus(List<string> recordIDs, List<string> statusCodes);

        /// <summary>
        ///  根据考核记录ID和人员ID集合获取非补考记录
        /// </summary>
        /// <param name="recordIDs">考核主记录ID集合</param>
        /// <param name="employeeIDs">人员工号</param>
        /// <returns></returns>
        Task<List<ExaminationMainInfo>> GetNotRetakeMainByRecordIDAndEmployeeIDs(List<string> recordIDs, List<string> employeeIDs);
    }
}
