﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IExaminationRecordRepository
    {
        /// <summary>
        /// 根据考核开始结束时间和考核类型获取数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="type"></param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetListByDate(DateTime startDate, DateTime endDate, string type, List<int> departmentIDs);

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        Task<ExaminationRecordInfo> GetDataByID(string recordID);

        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetListByIDs(List<string> recordIDs);

        /// <summary>
        /// 根据试卷ID获取数据
        /// </summary>
        /// <param name="paperMainIDs"></param>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetListByPaperMainIDs(List<string> paperMainIDs);

        /// <summary>
        /// 根据主键获取考核记录ID和考核名称的关系dict
        /// </summary>
        /// <param name="recordIDs">考核主记录集合</param>
        /// <returns></returns>
        Task<Dictionary<string, string>> GetRecordIDAndNameDictByIDs(List<string> recordIDs);

        /// <summary>
        /// 根据考核类型和主考人获取所有相关的考核记录主键ID
        /// </summary>
        /// <param name="examinationType"></param>
        /// <param name="examinerID">监考人</param>
        /// <returns></returns>
        Task<List<string>> GetRecordIDsByTypeAndExaminer(string examinationType, string examinerID);

        /// <summary>
        /// 获取未完成的实操类考核记录
        /// </summary>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetNotFinishedPracticalRecords();
        /// <summary>
        /// 获取所有相关的考核记录
        /// </summary>
        /// <param name="summaryQueryView">查询参数</param>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetListBySummaryQueryViewAsNoTrack(ExaminationSummaryQueryView summaryQueryView);

        /// <summary>
        /// 根据考核组ID获取考核记录列表
        /// </summary>
        /// <param name="groupID">考核组ID</param>
        /// <returns></returns>
        Task<List<ExaminationRecordInfo>> GetListByGroupID(string groupID);
    }
}
