
namespace NursingManagement.ViewModels
{
    /// <summary>
    /// 考核批次详情视图 - 用于ElementPlus嵌套表格的第二层数据
    /// </summary>
    public class ExaminationBatchDetailView
    {
        /// <summary>
        /// 考核记录ID
        /// </summary>
        public string ExaminationRecordID { get; set; }

        /// <summary>
        /// 考核名称
        /// </summary>
        public string ExamineName { get; set; }

        /// <summary>
        /// 考核级别名称
        /// </summary>
        public string ExaminationLevelName { get; set; }

        /// <summary>
        /// 监考人姓名（多个用顿号分隔）
        /// </summary>
        public string ExaminerName { get; set; }

        /// <summary>
        /// 应参加人数
        /// </summary>
        public int ShouldParticipateCount { get; set; }

        /// <summary>
        /// 实际参加人数
        /// </summary>
        public int ParticipateCount { get; set; }

        /// <summary>
        /// 未参加人数
        /// </summary>
        public int NotParticipateCount { get; set; }

        /// <summary>
        /// 合格人数（实操类为达标人数）
        /// </summary>
        public int PassCount { get; set; }

        /// <summary>
        /// 批次号（用于排序和显示）
        /// </summary>
        public int BatchNumber { get; set; }
        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime StartDateTime { get; set; }
        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime EndDateTime { get; set; }
    }
}
